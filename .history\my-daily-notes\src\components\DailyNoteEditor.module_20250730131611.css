/* DailyNoteEditor.module.css */
.container {
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: var(--spacer);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: var(--color-bg);
}
.controls {
  margin-bottom: 0.5rem;
  text-align: right;
}
.toggleButton {
  padding: 0.25rem 0.5rem;
  background-color: var(--color-secondary);
  color: var(--color-on-primary);
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: background-color 0.3s;
}
.toggleButton:hover,
.toggleButton:focus {
  background-color: var(--color-secondary-hover);
}
.contentArea {
  display: flex;
  gap: var(--spacer);
}
.textArea {
  width: 50%;
  min-height: 300px;
  padding: var(--spacer);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-family: monospace;
  resize: vertical;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.preview {
  width: 50%;
  min-height: 300px;
  padding: var(--spacer);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  overflow-y: auto;
  background-color: var(--color-bg);
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.singleArea {
  width: 100%;
  min-height: 300px;
  padding: var(--spacer);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  font-family: monospace;
  resize: vertical;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.5rem;
  gap: 0.5rem;
}
.cancelButton {
  padding: 0.5rem 1rem;
  background-color: var(--color-error);
  color: var(--color-on-primary);
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: background-color 0.3s;
}
.cancelButton:hover,
.cancelButton:focus {
  background-color: var(--color-error-hover);
}
.saveButton {
  padding: 0.5rem 1rem;
  background-color: var(--color-success);
  color: var(--color-on-primary);
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: background-color 0.3s;
}
.saveButton:hover,
.saveButton:focus {
  background-color: var(--color-success-hover);
}