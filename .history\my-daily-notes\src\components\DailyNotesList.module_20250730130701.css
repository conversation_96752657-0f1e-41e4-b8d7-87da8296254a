.container {
  width: 200px;
  flex-shrink: 0;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: var(--spacer);
  max-height: 70vh;
  overflow-y: auto;
}

.title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.listItem {
  padding: 0.5rem;
  cursor: pointer;
  background-color: transparent;
  border-radius: var(--radius);
  margin-bottom: 0.25rem;
  transition: background-color 0.2s;
}

.activeItem {
  padding: 0.5rem;
  cursor: pointer;
  background-color: var(--color-bg-active);
  border-radius: var(--radius);
  margin-bottom: 0.25rem;
  transition: background-color 0.2s;
}

.empty {
  padding: 0.5rem;
  color: #666;
}