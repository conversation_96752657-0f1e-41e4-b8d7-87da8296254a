// GoalsEditor.js
import React, { useState } from 'react';

const GoalsEditor = ({ initialContent, onSave, onCancel }) => {
  const [content, setContent] = useState(initialContent);

  const handleChange = (e) => {
    setContent(e.target.value);
  };

  const handleSave = () => {
    onSave(content);
  };

  return (
    <div style={{ 
      border: '1px solid #ddd', 
      borderRadius: '4px',
      padding: '0.5rem',
      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
    }}>
      <div style={{ marginBottom: '0.5rem' }}>
        <small style={{ color: '#666' }}>
          Edit your goals file. The timeline will update automatically when you save.
        </small>
      </div>
      <textarea
        value={content}
        onChange={handleChange}
        style={{
          width: '100%',
          minHeight: '400px',
          padding: '0.5rem',
          border: '1px solid #ddd',
          borderRadius: '4px',
          fontFamily: 'monospace',
          resize: 'vertical'
        }}
      />
      <div style={{ 
        display: 'flex', 
        justifyContent: 'flex-end',
        marginTop: '0.5rem',
        gap: '0.5rem'
      }}>
        <button 
          onClick={onCancel}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
        <button 
          onClick={handleSave}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default GoalsEditor;
