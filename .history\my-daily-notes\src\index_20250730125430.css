/* Reset and Base Styles */
:root {
  --color-primary: #2196F3;
  --color-primary-hover: #1976D2;
  --color-on-primary: #FFFFFF;
  --color-border: #DDDDDD;
  --color-bg: #FAFAFA;
  --font-family: system-ui, -apple-system, sans-serif;
  --radius: 4px;
  --spacer: 1rem;
}

/* Dark theme overrides */
body[data-theme='dark'] {
  --color-primary: #90caf9;
  --color-primary-hover: #64b5f6;
  --color-on-primary: #000000;
  --color-border: #555555;
  --color-bg: #333333;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--color-bg);
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}
