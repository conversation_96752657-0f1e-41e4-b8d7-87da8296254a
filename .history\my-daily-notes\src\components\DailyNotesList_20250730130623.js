// DailyNotesList.js
import React from 'react';

const DailyNotesList = ({ notes, selectedNote, onSelectNote }) => {
  // newest-first
  const sortedNotes = [...notes].sort((a, b) => b.localeCompare(a));

  return (
    <div
      style={{
        width: '200px',
        flexShrink: 0,            // never shrink below 200px
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '0.5rem',
        maxHeight: '70vh',        // so it fits in viewport
        overflowY: 'auto',
      }}
    >
      <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem' }}>
        Daily Notes
      </h3>
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {sortedNotes.length === 0 ? (
          <li style={{ padding: '0.5rem', color: '#666' }}>
            No notes found
          </li>
        ) : (
          sortedNotes.map((note) => (
import styles from './DailyNotesList.module.css';

            <li
              key={note}
              onClick={() => onSelectNote(note)}
              className={
                note === selectedNote ? styles.activeItem : styles.listItem
              }
            >
              {note === new Date().toISOString().slice(0, 10) ? (
                <strong>{note} (Today)</strong>
              ) : (
                note
              )}
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default DailyNotesList;
