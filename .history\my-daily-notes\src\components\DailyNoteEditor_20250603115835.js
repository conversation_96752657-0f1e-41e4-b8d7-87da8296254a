// DailyNoteEditor.js
import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const DailyNoteEditor = ({ initialContent, onSave, onCancel }) => {
  const [content, setContent] = useState(initialContent);
  const [showLivePreview, setShowLivePreview] = useState(true);

  const handleTogglePreview = () => {
    setShowLivePreview(prev => !prev);
  };

  return (
    <div
      style={{
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '0.5rem',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
      }}
    >
      <div style={{ marginBottom: '0.5rem', textAlign: 'right' }}>
        <button
          onClick={handleTogglePreview}
          style={{
            padding: '0.25rem 0.5rem',
            backgroundColor: '#78909C',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          {showLivePreview ? 'Hide Live Preview' : 'Show Live Preview'}
        </button>
      </div>
      {showLivePreview ? (
        <div style={{ display: 'flex', gap: '1rem' }}>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            style={{
              width: '50%',
              minHeight: '300px',
              padding: '0.5rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontFamily: 'monospace',
              resize: 'vertical',
            }}
          />
          <div
            style={{
              width: '50%',
              minHeight: '300px',
              padding: '0.5rem',
              border: '1px solid #ddd',
              borderRadius: '4px',
              overflowY: 'auto',
              background: '#fafafa'
            }}
          >
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {content}
            </ReactMarkdown>
          </div>
        </div>
      ) : (
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          style={{
            width: '100%',
            minHeight: '300px',
            padding: '0.5rem',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontFamily: 'monospace',
            resize: 'vertical',
          }}
        />
      )}
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '0.5rem',
          gap: '0.5rem',
        }}
      >
        <button
          onClick={onCancel}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Cancel
        </button>
        <button
          onClick={() => onSave(content)}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default DailyNoteEditor;
