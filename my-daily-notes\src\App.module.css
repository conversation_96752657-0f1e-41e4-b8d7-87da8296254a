/* App.module.css */

.appContainer {
  max-width: 1000px;
  margin: 2rem auto;
  font-family: var(--font-family);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section {
  margin-bottom: 2rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.button {
  padding: 0.5rem 1rem;
  background-color: var(--color-primary);
  color: var(--color-on-primary);
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: background-color 0.3s;
}

.button:hover,
.button:focus {
  background-color: var(--color-primary-hover);
}

.plotContainer {
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
}

.layout {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 1rem;
}

@media (max-width: 600px) {
  .layout {
    grid-template-columns: 1fr;
  }
}

.editorArea {
  flex: 1;
}

.markdownViewer {
  min-height: 200px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: var(--spacer);
}