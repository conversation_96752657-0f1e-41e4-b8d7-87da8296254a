// App.js
import React, { useEffect, useState, useRef, useContext } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import Plotly from 'plotly.js-dist-min';
import DailyNotesList from './components/DailyNotesList';
import DailyNoteEditor from './components/DailyNoteEditor';
import GoalsEditor from './components/GoalsEditor';

import { ThemeContext } from './context/ThemeContext';
import styles from './App.module.css';
import { FaSun, FaMoon } from 'react-icons/fa';

// Detect native File System Access API support
const supportsDirectoryPicker = 'showDirectoryPicker' in window;
const DEFAULT_FOLDER_NAME = 'C:\\Users\\<USER>\\OneDrive\\Obsidian\\01-JOURNAL\\01-DAILY';

function App() {
  // ── State ───────────────────────────────────────────────────────────────────
  const [goalsMd, setGoalsMd] = useState('');
  const [dailyMd, setDailyMd] = useState('Please select a folder with your daily notes.');
  const [dailyNoteNames, setDailyNoteNames] = useState([]);   // ["2025-06-01", "2025-06-02", …]
  const [selectedNote, setSelectedNote] = useState(null);
  const [isEditingNote, setIsEditingNote] = useState(false);
  const [isEditingGoals, setIsEditingGoals] = useState(false);
  const todayISO = new Date().toISOString().slice(0, 10);

  // For FS-API
  const [folderHandle, setFolderHandle] = useState(null);
  const fileHandlesRef = useRef({});
  const fileInputRef = useRef(null);

  // Refs for Plotly containers
  const fullPlotRef = useRef(null);
  const weekPlotRef = useRef(null);

  // ── Folder Selection & File Loading ─────────────────────────────────────────

  const handleSelectFolder = async () => {
    if (supportsDirectoryPicker) {
      try {
        const handle = await window.showDirectoryPicker();
        setFolderHandle(handle);
        await loadDailyNoteFiles(handle);
      } catch (err) {
        console.error('Directory picker failed or was cancelled', err);
      }
    } else {
      fileInputRef.current.click();
    }
  };

  const loadDailyNoteFiles = async (handle) => {
    const names = [];
    const handles = {};
    for await (const entry of handle.values()) {
      if (entry.kind === 'file' && entry.name.endsWith('.md')) {
        const noteName = entry.name.replace(/\.md$/i, '');
        names.push(noteName);
        handles[noteName] = entry;
      }
    }
    setDailyNoteNames(names);
    fileHandlesRef.current = handles;
    if (names.length) {
      const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
      setSelectedNote(sorted[sorted.length - 1]);
    } else {
      setDailyMd('No Markdown files found in this folder.');
    }
  };

  const handleFallbackFiles = (e) => {
    const files = Array.from(e.target.files);
    const names = [];
    const handles = {};
    files.forEach(file => {
      if (file.name.endsWith('.md')) {
        const noteName = file.name.replace(/\.md$/i, '');
        names.push(noteName);
        handles[noteName] = file;
      }
    });
    setDailyNoteNames(names);
    fileHandlesRef.current = handles;
    if (names.length) {
      const sorted = [...names].sort((a, b) => new Date(a) - new Date(b));
      setSelectedNote(sorted[sorted.length - 1]);
    } else {
      setDailyMd('No Markdown files found in this folder.');
    }
  };

  const loadNoteContent = async (noteName) => {
    const handleOrFile = fileHandlesRef.current[noteName];
    if (!handleOrFile) return;
    try {
      let file;
      if ('getFile' in handleOrFile) {
        file = await handleOrFile.getFile();
      } else {
        file = handleOrFile;
      }
      const text = await file.text();
      setDailyMd(text);
    } catch (err) {
      console.error('Error reading note:', err);
      setDailyMd('Error reading note.');
    }
  };

  useEffect(() => {
    if (selectedNote) loadNoteContent(selectedNote);
  }, [selectedNote]);

  // ── Note Handlers ───────────────────────────────────────────────────────────

  const handleNoteSelect = (noteName) => {
    setSelectedNote(noteName);
    setIsEditingNote(false);
    loadNoteContent(noteName);
  };

  const handleCreateNote = () => {
    setSelectedNote(todayISO);
    setDailyMd('');
    setIsEditingNote(true);
    if (!dailyNoteNames.includes(todayISO)) {
      setDailyNoteNames(prev => [...prev, todayISO]);
    }
  };

  const handleSaveNote = async (content) => {
    const handleOrFile = fileHandlesRef.current[selectedNote];

    if (handleOrFile && 'createWritable' in handleOrFile) {
      try {
        const writable = await handleOrFile.createWritable();
        await writable.write(content);
        await writable.close();
        setDailyMd(content);
        setIsEditingNote(false);
      } catch (err) {
        console.error('Error saving note:', err);
      }
    } else {
      const newFile = new Blob([content], { type: 'text/markdown' });
      const fileUrl = URL.createObjectURL(newFile);
      const a = document.createElement('a');
      a.href = fileUrl;
      a.download = `${selectedNote}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(fileUrl);
    }
  };

  const handleEditNote = () => { setIsEditingNote(true); };

  // ── Goals Loading & Plot Generation ─────────────────────────────────────────

  useEffect(() => {
    fetch('/goals.md')
      .then(r => { if (!r.ok) throw new Error('Failed to load goals.md'); return r.text(); })
      .then(setGoalsMd)
      .catch(err => console.error(err));
  }, []);

  useEffect(() => {
    if (!goalsMd) return;

    const dataMatch = /# DATA([\s\S]*?)(?:# NOTES|$)/.exec(goalsMd);
    if (!dataMatch) return;
    const dataSection = dataMatch[1].trim();

    function parseTable(table) {
      const rows = table.split('\n')
        .filter(Boolean)
        .map(r => r.split('|').map(c => c.trim()));
      const headers = rows[0].filter(Boolean);
      const items = rows.slice(2).map(row => {
        const obj = {};
        headers.forEach((h, i) => obj[h] = row[i + 1] || '');
        if (obj.Task.startsWith('-')) {
          obj.isSubtask = true;
          obj.Task = obj.Task.slice(1).trim();
        } else {
          obj.isSubtask = false;
        }
        return obj;
      });
      const out = [];
      let current = null;
      for (let i = 0; i < items.length; i++) {
        if (!items[i].isSubtask) {
          current = items[i];
          out.push(current);
          const subtasks = [];
          let j = i + 1;
          while (j < items.length && items[j].isSubtask) {
            items[j].parentTask = current.Task;
            subtasks.push(items[j]);
            j++;
          }
          if (subtasks.length) {
            const startDate = new Date(current.Start);
            const finishDate = new Date(current.Finish);
            const span = finishDate - startDate;
            const per = span / subtasks.length;
            subtasks.forEach((st, k) => {
              if (!st.Start)
                st.Start = new Date(startDate.getTime() + per * k).toISOString().slice(0, 10);
              if (!st.Finish)
                st.Finish = new Date(startDate.getTime() + per * (k + 1)).toISOString().slice(0, 10);
              st.Percent = st.Percent || '0';
              st.State = st.State || 'Incomplete';
            });
          }
          out.push(...subtasks);
          i = j - 1;
        }
      }
      return out;
    }

    function parseProjects(text) {
      const lines = text.split('\n');
      const projects = [];
      let project = null;
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.startsWith('## ')) {
          project = { ProjectName: line.slice(3), Tasks: [] };
        } else if (line.startsWith('- ProjectColor:') && project) {
          project.ProjectColor = line.split(':')[1].trim();
        } else if (line.startsWith('|') && project) {
          const block = [];
          while (i < lines.length && lines[i].startsWith('|')) {
            block.push(lines[i]);
            i++;
          }
          project.Tasks = parseTable(block.join('\n'));
          projects.push(project);
          project = null;
          i--;
        }
      }
      return projects;
    }

    function generateTimeMarks(year) {
      const months = [
        'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE',
        'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'
      ];
      const curMonth = new Date().getMonth();
      const nextMonth = (curMonth + 1) % 12;
      const marks = [curMonth, nextMonth].map(m => {
        const start = `${year}-${String(m + 1).padStart(2, '0')}-01`;
        const dayCount = new Date(year, m + 1, 0).getDate();
        const finish = `${year}-${String(m + 1).padStart(2, '0')}-${dayCount}`;
        return { Task: months[m], Start: start, Finish: finish, Percent: '100', State: 'Complete' };
      });
      return [{ ProjectName: 'TIMEMARKS', ProjectColor: 'blue', Tasks: marks }];
    }

    const userProjects = parseProjects(dataSection);
    const allStarts = userProjects.flatMap(p => p.Tasks.map(t => t.Start)).filter(Boolean);
    const years = allStarts.map(s => +s.slice(0, 4)).sort();
    const refYear = years[0] || new Date().getFullYear();
    const twoMonthsAgo = new Date(); twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 1);

    const entries = [
      ...generateTimeMarks(refYear),
      ...userProjects
    ]
      .reverse()
      .map(proj => ({
        ...proj,
        Tasks: proj.Tasks.filter(t => new Date(t.Finish) > twoMonthsAgo)
      }))
      .filter(p => p.Tasks.length);

    const data = [], shapes = [], fullA = [], weekA = [], yTicks = [];
    let counter = 0;
    const weekStart = new Date(); weekStart.setHours(0, 0, 0, 0);
    const weekEnd = new Date(weekStart); weekEnd.setDate(weekStart.getDate() + 7);
    const wsISO = weekStart.toISOString().slice(0, 10);
    const weISO = weekEnd.toISOString().slice(0, 10);

    function dateAtPercent(s, f, p) {
      const sd = new Date(s), fd = new Date(f);
      return new Date(sd.getTime() + (fd - sd) * (p / 100)).toISOString().slice(0, 10);
    }

    function addRect(x0, x1, c, y0, y1, o = 0.5) {
      shapes.push({
        type: 'rect', x0, x1, y0, y1, xref: 'x', yref: 'y',
        fillcolor: c, opacity: o, line: { width: 0 }
      });
    }

    entries.forEach(proj => {
      yTicks.push(proj.ProjectName);
      const subs = proj.Tasks.filter(t => t.isSubtask);
      const mains = proj.Tasks.filter(t => !t.isSubtask);
      const byParent = {};
      subs.forEach(s => { byParent[s.parentTask] = byParent[s.parentTask] || []; byParent[s.parentTask].push(s); });

      const sorted = [];
      mains.forEach(m => {
        sorted.push(m);
        if (byParent[m.Task]) sorted.push(...byParent[m.Task]);
      });

      let inner = 0;
      sorted.forEach(task => {
        const sub = task.isSubtask;
        const yPos = counter + inner * 0.2;
        const baseO = (task.State === 'Complete' ? 0.2 : 0.5);
        const barO = sub ? baseO * 0.5 : baseO;

        addRect(task.Start, task.Finish, proj.ProjectColor,
          yPos - (sub ? 0.03 : 0.05), yPos + (sub ? 0.03 : 0.05), barO);

        if (task.State !== 'Not Started') {
          const end = task.State === 'Incomplete'
            ? dateAtPercent(task.Start, task.Finish, +task.Percent)
            : task.Finish;
          addRect(task.Start, end, 'black',
            yPos - (sub ? 0.03 : 0.07), yPos + (sub ? 0.03 : 0.07), sub ? 0.7 : 1);
        }

        data.push({
          type: 'scatter', mode: 'lines',
          x: [task.Start, task.Finish], y: [yPos, yPos],
          hoverinfo: 'text',
          text: `${task.Task}<br>${task.Start} → ${task.Finish}<br>${task.State}`,
          line: { width: 8, color: 'rgba(0,0,0,0)' }
        });

        fullA.push({
          x: new Date(new Date(task.Finish).getTime() + 2 * 864e5),
          y: yPos, xref: 'x', yref: 'y',
          text: (sub ? '↳ ' : '') + task.Task,
          showarrow: false,
          font: { family: 'Arial Black, sans-serif', size: sub ? 10 : 12, color: '#000' },
          align: 'left'
        });

        const ts = new Date(task.Start), tf = new Date(task.Finish);
        if (tf >= weekStart && ts <= weekEnd) {
          const vs = ts < weekStart ? weekStart : ts;
          const ve = tf > weekEnd ? weekEnd : tf;
          const ax = new Date((vs.getTime() + ve.getTime()) / 2);
          weekA.push({
            x: ax, y: yPos, xref: 'x', yref: 'y',
            text: (sub ? '↳ ' : '') + task.Task,
            showarrow: false,
            font: { family: 'Arial Black, sans-serif', size: sub ? 10 : 12, color: '#000' },
            align: 'center', bgcolor: 'rgba(255,255,255,0.7)',
            bordercolor: '#ddd', borderwidth: 1, borderpad: 2
          });
        }
        inner++;
      });

      counter += inner * 0.2 + 0.2;
      for (let i = 0; i < inner - 1; i++) yTicks.push('');
    });

    shapes.push({
      type: 'line', x0: todayISO, x1: todayISO, y0: 0, y1: counter,
      line: { color: 'darkred', width: 2, dash: 'dot' }
    });

    const fullLayout = {
      height: 400,
      margin: { l: 150, r: 150, t: 30, b: 50 },
      xaxis: {
        type: 'date',
        rangeselector: {
          buttons: [
            { count: 7, step: 'day', stepmode: 'backward', label: '1w' },
            { count: 1, step: 'month', stepmode: 'backward', label: '1m' },
            { step: 'all', label: 'All' }
          ]
        }
      },
      yaxis: {
        ticktext: yTicks,
        tickvals: yTicks.map((_, i) => i * 0.2),
        autorange: 'reversed'
      },
      shapes,
      annotations: fullA,
      showlegend: false
    };

    const weekLayout = {
      height: 400,
      margin: { l: 150, r: 30, t: 30, b: 50 },
      xaxis: { type: 'date', autorange: false, range: [wsISO, weISO] },
      yaxis: {
        ticktext: yTicks,
        tickvals: yTicks.map((_, i) => i * 0.2),
        autorange: 'reversed'
      },
      shapes,
      annotations: weekA,
      showlegend: false
    };

    Plotly.react(fullPlotRef.current, data, fullLayout, { responsive: true });
    Plotly.react(weekPlotRef.current, data, weekLayout, { responsive: true });
  }, [goalsMd, todayISO]);

  // ── Goals Handlers ─────────────────────────────────────────────────────────

  const handleEditGoals = () => { setIsEditingGoals(true); };
  const handleSaveGoals = (content) => {
    setGoalsMd(content);
    setIsEditingGoals(false);
  };

  // ── Render ─────────────────────────────────────────────────────────────────

  return (
    <div style={{ maxWidth: 1000, margin: '2rem auto', fontFamily: 'system-ui, -apple-system, sans-serif' }}>
      <header><h1>My Daily Notes App</h1></header>
      <main>

        {/* 1) Folder Selection */}
        {dailyNoteNames.length === 0 && !folderHandle && (
          <section style={{ marginBottom: '2rem' }}>
            <p>
              Welcome! It seems like you haven't selected your daily notes folder yet.
              {supportsDirectoryPicker ? (
                <>
                  Please select the <b>{DEFAULT_FOLDER_NAME}</b> folder, or any other folder
                  containing your notes.
                </>
              ) : (
                <>
                  Your browser doesn't support the native folder picker. Please choose your daily notes folder using the button below.
                </>
              )}
            </p>

            <button onClick={handleSelectFolder}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#2196F3',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer'
              }}
            >
              Select Daily Notes Folder
            </button>
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: 'none' }}
              webkitdirectory="true"
              directory="true"
              multiple
              onChange={handleFallbackFiles}
            />
          </section>
        )}

        {/* 2) Goals Section */}
        <section style={{ marginBottom: '2rem' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '1rem'
          }}>
            <h2>Goals</h2>
            {!isEditingGoals && (
              <button onClick={handleEditGoals}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#2196F3',
                  color: 'white',
                  border: 'none',
                  borderRadius: 4,
                  cursor: 'pointer'
                }}
              >
                Edit Goals
              </button>
            )}
          </div>
          {isEditingGoals
            ? <GoalsEditor initialContent={goalsMd} onSave={handleSaveGoals} onCancel={() => setIsEditingGoals(false)} />
            : <>
                <div style={{ marginBottom: '2rem' }}>
                  <h3>Goals Timeline</h3>
                  <div ref={fullPlotRef} style={{ border: '1px solid #eee', borderRadius: 4 }} />
                </div>
                <div>
                  <h3>This Week</h3>
                  <div ref={weekPlotRef} style={{ border: '1px solid #eee', borderRadius: 4 }} />
                </div>
              </>
          }
        </section>

        {/* 3) Daily Notes Section */}
        {dailyNoteNames.length > 0 && (
          <section style={{ marginBottom: '2rem' }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1rem'
            }}>
              <h2>Note ({selectedNote})</h2>
              <div>
                <button onClick={handleCreateNote}
                  style={{
                    marginRight: 8,
                    padding: '0.5rem 1rem',
                    backgroundColor: '#4CAF50',
                    color: 'white',
                    border: 'none',
                    borderRadius: 4,
                    cursor: 'pointer'
                  }}
                >
                  New Today's Note
                </button>
                {!isEditingNote && (
                  <button onClick={handleEditNote}
                    style={{
                      padding: '0.5rem 1rem',
                      backgroundColor: '#2196F3',
                      color: 'white',
                      border: 'none',
                      borderRadius: 4,
                      cursor: 'pointer'
                    }}
                  >
                    Edit Note
                  </button>
                )}
              </div>
            </div>
            <div style={{ display: 'flex', gap: 16 }}>
              <DailyNotesList notes={dailyNoteNames} selectedNote={selectedNote} onSelectNote={handleNoteSelect} />
              <div style={{ flex: 1 }}>
                {isEditingNote
                  ? <DailyNoteEditor initialContent={dailyMd} onSave={handleSaveNote} onCancel={() => setIsEditingNote(false)} />
                  : <article style={{
                      border: '1px solid #ddd',
                      padding: 16,
                      borderRadius: 4,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                      minHeight: 300
                    }}>
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {dailyMd}
                      </ReactMarkdown>
                    </article>
                }
              </div>
            </div>
          </section>
        )}

      </main>
    </div>
  );
}

export default App;
