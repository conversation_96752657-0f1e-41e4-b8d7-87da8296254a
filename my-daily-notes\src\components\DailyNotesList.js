import React from 'react';
import styles from './DailyNotesList.module.css';

const DailyNotesList = ({ notes, selectedNote, onSelectNote }) => {
  // newest-first
  const sortedNotes = [...notes].sort((a, b) => b.localeCompare(a));

  return (
    <div className={styles.container}>
      <h3 className={styles.title}>
        Daily Notes
      </h3>
      <ul className={styles.list}>
        {sortedNotes.length === 0 ? (
          <li className={styles.empty}>
            No notes found
          </li>
        ) : (
          sortedNotes.map((note) => (
            <li
              key={note}
              onClick={() => onSelectNote(note)}
              className={
                note === selectedNote ? styles.activeItem : styles.listItem
              }
            >
              {note === new Date().toISOString().slice(0, 10) ? (
                <strong>{note} (Today)</strong>
              ) : (
                note
              )}
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default DailyNotesList;
